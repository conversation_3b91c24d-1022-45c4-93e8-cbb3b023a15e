<template>
  <div class="status-bar">
    <span>{{ currentTime }}</span>
    <span>
      <i class="fas fa-signal"></i> 
      <i class="fas fa-wifi"></i> 
      <i class="fas fa-battery-three-quarters"></i>
    </span>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'StatusBar',
  computed: {
    ...mapGetters('app', ['currentTime'])
  },
  mounted() {
    // 每分钟更新一次时间
    this.updateTime()
    this.timer = setInterval(() => {
      this.updateTime()
    }, 60000)
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    updateTime() {
      this.$store.dispatch('app/updateTime')
    }
  }
}
</script>

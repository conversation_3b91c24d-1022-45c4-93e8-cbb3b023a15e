<template>
  <div class="app-container">
    <StatusBar />
    <TopNav title="VIP会员" :show-back="true" />
    
    <!-- 内容区域 -->
    <div class="content">
      <!-- VIP状态卡片 -->
      <div class="card mb-6">
        <div class="flex items-center space-x-4">
          <div class="w-16 h-16 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
            <i class="fas fa-crown text-white text-2xl"></i>
          </div>
          <div class="flex-1">
            <h3 class="text-white font-bold text-lg">{{ vipStatusText }}</h3>
            <p class="text-gray-400 text-sm">{{ vipDescription }}</p>
            <p v-if="isVip && vipExpireDate" class="text-purple-300 text-xs mt-1">
              到期时间：{{ vipExpireDate }}
            </p>
          </div>
        </div>
      </div>
      
      <!-- VIP套餐 -->
      <div class="mb-6">
        <h3 class="text-xl font-bold text-white mb-4 flex items-center">
          <i class="fas fa-gem mr-2 text-yellow-400"></i>
          VIP套餐
        </h3>
        
        <div class="space-y-4">
          <div 
            v-for="package_ in vipPackages" 
            :key="package_.id"
            class="card cursor-pointer hover:bg-opacity-20 transition-all"
            @click="selectPackage(package_)"
          >
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <div class="flex items-center space-x-2 mb-2">
                  <h4 class="text-white font-semibold text-lg">{{ package_.name }}</h4>
                  <span v-if="package_.isPopular" class="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                    热门
                  </span>
                </div>
                <p class="text-gray-400 text-sm mb-2">{{ package_.description }}</p>
                <div class="flex items-center space-x-4">
                  <span class="text-yellow-400 font-bold text-xl">¥{{ package_.price }}</span>
                  <span v-if="package_.originalPrice" class="text-gray-500 line-through text-sm">
                    ¥{{ package_.originalPrice }}
                  </span>
                  <span class="text-purple-300 text-sm">{{ package_.duration }}</span>
                </div>
              </div>
              <button class="btn-primary">
                选择
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- VIP权益 -->
      <div class="mb-6">
        <h3 class="text-xl font-bold text-white mb-4 flex items-center">
          <i class="fas fa-star mr-2 text-purple-400"></i>
          VIP权益
        </h3>
        
        <div class="grid grid-cols-2 gap-4">
          <div 
            v-for="benefit in vipBenefits" 
            :key="benefit.id"
            class="card text-center"
          >
            <div :class="['w-12 h-12 rounded-full mx-auto mb-3 flex items-center justify-center', benefit.bgClass]">
              <i :class="['text-white text-xl', benefit.icon]"></i>
            </div>
            <h4 class="text-white font-semibold text-sm mb-1">{{ benefit.title }}</h4>
            <p class="text-gray-400 text-xs">{{ benefit.description }}</p>
          </div>
        </div>
      </div>
      
      <!-- 购买说明 -->
      <div class="card">
        <h4 class="text-white font-semibold mb-3">购买说明</h4>
        <div class="text-gray-400 text-sm space-y-2">
          <p>• VIP会员自购买之日起生效</p>
          <p>• 支持微信、支付宝、银行卡支付</p>
          <p>• 购买后不支持退款，请谨慎选择</p>
          <p>• 如有问题请联系客服</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import StatusBar from '@/components/StatusBar.vue'
import TopNav from '@/components/TopNav.vue'
import { mapGetters } from 'vuex'
import { vipApi } from '@/api/modules/vip'

export default {
  name: 'VIP',
  components: {
    StatusBar,
    TopNav
  },
  data() {
    return {
      vipPackages: [
        {
          id: 1,
          name: 'VIP月卡',
          description: '享受一个月的VIP特权',
          price: 29,
          originalPrice: 39,
          duration: '30天',
          isPopular: false
        },
        {
          id: 2,
          name: 'VIP季卡',
          description: '享受三个月的VIP特权',
          price: 79,
          originalPrice: 117,
          duration: '90天',
          isPopular: true
        },
        {
          id: 3,
          name: 'VIP年卡',
          description: '享受一年的VIP特权',
          price: 299,
          originalPrice: 468,
          duration: '365天',
          isPopular: false
        },
        {
          id: 4,
          name: '超级VIP年卡',
          description: '享受一年的超级VIP特权',
          price: 599,
          originalPrice: 999,
          duration: '365天',
          isPopular: false
        }
      ],
      vipBenefits: [
        {
          id: 1,
          title: '免费观看',
          description: '所有付费内容免费看',
          icon: 'fas fa-play-circle',
          bgClass: 'bg-gradient-to-br from-purple-500 to-pink-500'
        },
        {
          id: 2,
          title: '高清下载',
          description: '支持高清内容下载',
          icon: 'fas fa-download',
          bgClass: 'bg-gradient-to-br from-blue-500 to-purple-500'
        },
        {
          id: 3,
          title: '专属客服',
          description: '享受VIP专属客服',
          icon: 'fas fa-headset',
          bgClass: 'bg-gradient-to-br from-green-500 to-blue-500'
        },
        {
          id: 4,
          title: '无广告',
          description: '享受无广告体验',
          icon: 'fas fa-shield-alt',
          bgClass: 'bg-gradient-to-br from-red-500 to-pink-500'
        },
        {
          id: 5,
          title: '优先更新',
          description: '优先体验新功能',
          icon: 'fas fa-rocket',
          bgClass: 'bg-gradient-to-br from-yellow-500 to-red-500'
        },
        {
          id: 6,
          title: '专属标识',
          description: 'VIP专属身份标识',
          icon: 'fas fa-crown',
          bgClass: 'bg-gradient-to-br from-indigo-500 to-purple-500'
        }
      ],
      vipExpireDate: null
    }
  },
  computed: {
    ...mapGetters('user', ['isVip', 'vipLevel', 'vipLevelText']),
    
    vipStatusText() {
      return this.isVip ? this.vipLevelText : '普通用户'
    },
    
    vipDescription() {
      if (this.isVip) {
        return '感谢您的支持，享受VIP特权'
      }
      return '开通VIP，解锁更多精彩内容'
    }
  },
  async mounted() {
    await this.loadVipData()
  },
  methods: {
    async loadVipData() {
      try {
        // 加载VIP套餐信息
        const packagesData = await vipApi.getVipPackages()
        if (packagesData && packagesData.success) {
          this.vipPackages = packagesData.packages
        }
        
        // 加载VIP权益信息
        const benefitsData = await vipApi.getVipBenefits()
        if (benefitsData && benefitsData.success) {
          this.vipBenefits = benefitsData.benefits
        }
        
        // 如果用户已是VIP，获取到期时间
        if (this.isVip) {
          const statusData = await vipApi.getVipStatus()
          if (statusData && statusData.success) {
            this.vipExpireDate = statusData.expireDate
          }
        }
      } catch (error) {
        console.error('加载VIP数据失败:', error)
      }
    },
    
    async selectPackage(package_) {
      if (!this.$store.getters['user/isLoggedIn']) {
        alert('请先登录')
        this.$router.push('/login')
        return
      }
      
      const confirmed = confirm(`确认购买 ${package_.name}？\n价格：¥${package_.price}\n时长：${package_.duration}`)
      if (!confirmed) return
      
      try {
        const result = await vipApi.purchaseVip({
          packageId: package_.id,
          price: package_.price
        })
        
        if (result.success) {
          alert('购买成功！感谢您的支持')
          // 更新用户VIP状态
          this.$store.commit('user/SET_VIP_STATUS', { 
            isVip: true, 
            level: package_.id === 4 ? 2 : 1 
          })
          await this.loadVipData()
        } else {
          if (result.code === 'INSUFFICIENT_BALANCE') {
            const goRecharge = confirm('余额不足，是否前往充值？')
            if (goRecharge) {
              this.$router.push('/recharge')
            }
          } else {
            alert(result.message || '购买失败')
          }
        }
      } catch (error) {
        console.error('购买VIP失败:', error)
        alert('购买失败，请稍后重试')
      }
    }
  }
}
</script>

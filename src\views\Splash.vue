<template>
  <div class="app-container">
    <StatusBar />
    
    <!-- 开屏大图 -->
    <div class="relative h-full flex flex-col justify-center items-center">
      <div class="absolute inset-0 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900"></div>
      <div class="absolute inset-0 bg-black bg-opacity-40"></div>
      
      <!-- 主要内容 -->
      <div class="relative z-10 text-center">
        <!-- Logo区域 -->
        <div class="mb-8">
          <div class="w-32 h-32 bg-gradient-to-br from-white to-purple-200 rounded-full mx-auto mb-6 flex items-center justify-center shadow-2xl">
            <i class="fas fa-camera text-purple-900 text-5xl"></i>
          </div>
          <h1 class="text-4xl font-bold text-white mb-2">JANS</h1>
          <p class="text-xl text-purple-200">时尚先锋摄影</p>
        </div>
        
        <!-- 标语 -->
        <div class="mb-12">
          <h2 class="text-2xl font-light text-white mb-4">发现时尚之美</h2>
          <p class="text-purple-200 text-lg">专业摄影 · 精品内容 · 独家体验</p>
        </div>
        
        <!-- 进入按钮 -->
        <button @click="enterApp" class="btn-primary text-lg px-8 py-4">
          <i class="fas fa-arrow-right mr-2"></i>
          立即体验
        </button>
      </div>
      
      <!-- 底部装饰 -->
      <div class="absolute bottom-10 left-1/2 transform -translate-x-1/2">
        <div class="flex space-x-2">
          <div class="w-2 h-2 bg-white rounded-full animate-pulse"></div>
          <div class="w-2 h-2 bg-white bg-opacity-50 rounded-full animate-pulse" style="animation-delay: 0.2s"></div>
          <div class="w-2 h-2 bg-white bg-opacity-30 rounded-full animate-pulse" style="animation-delay: 0.4s"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import StatusBar from '@/components/StatusBar.vue'

export default {
  name: 'Splash',
  components: {
    StatusBar
  },
  mounted() {
    // 3秒后自动跳转到首页
    setTimeout(() => {
      this.enterApp()
    }, 3000)
  },
  methods: {
    enterApp() {
      this.$router.push('/home')
    }
  }
}
</script>

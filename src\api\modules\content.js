import api from '../index'

export const contentApi = {
  // 获取首页数据
  getHomeData() {
    return api.get('/content/home')
  },
  
  // 获取模特列表
  getModels(params) {
    return api.get('/content/models', { params })
  },
  
  // 获取模特详情
  getModelDetail(id) {
    return api.get(`/content/models/${id}`)
  },
  
  // 获取杂志列表
  getMagazines(params) {
    return api.get('/content/magazines', { params })
  },
  
  // 获取杂志详情
  getMagazineDetail(id) {
    return api.get(`/content/magazines/${id}`)
  },
  
  // 获取杂志内容
  getMagazineContent(id) {
    return api.get(`/content/magazines/${id}/content`)
  },
  
  // 获取社区帖子
  getCommunityPosts(params) {
    return api.get('/content/community/posts', { params })
  },
  
  // 获取帖子详情
  getPostDetail(id) {
    return api.get(`/content/community/posts/${id}`)
  },
  
  // 获取排行榜数据
  getRankingData(type) {
    return api.get(`/content/ranking/${type}`)
  },
  
  // 搜索内容
  search(params) {
    return api.get('/content/search', { params })
  }
}

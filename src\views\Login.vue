<template>
  <div class="app-container">
    <StatusBar />
    <TopNav title="登录" :show-back="true" />
    
    <!-- 内容区域 -->
    <div class="content flex flex-col justify-center">
      <div class="text-center mb-8">
        <div class="w-24 h-24 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full mx-auto mb-4 flex items-center justify-center">
          <i class="fas fa-user text-white text-3xl"></i>
        </div>
        <h2 class="text-2xl font-bold text-white mb-2">欢迎回来</h2>
        <p class="text-gray-400">登录您的账户继续精彩体验</p>
      </div>
      
      <form @submit.prevent="handleLogin" class="space-y-4">
        <div>
          <label class="block text-white text-sm font-medium mb-2">用户名</label>
          <input 
            v-model="loginForm.username"
            type="text" 
            class="input-field" 
            placeholder="请输入用户名"
            required
          >
        </div>
        
        <div>
          <label class="block text-white text-sm font-medium mb-2">密码</label>
          <input 
            v-model="loginForm.password"
            type="password" 
            class="input-field" 
            placeholder="请输入密码"
            required
          >
        </div>
        
        <div>
          <label class="block text-white text-sm font-medium mb-2">验证码</label>
          <div class="flex space-x-2">
            <input 
              v-model="loginForm.captcha"
              type="text" 
              class="input-field flex-1" 
              placeholder="请输入验证码"
              required
            >
            <div 
              @click="refreshCaptcha"
              class="w-24 h-12 bg-gray-600 rounded-lg flex items-center justify-center text-white font-bold cursor-pointer hover:bg-gray-500"
            >
              {{ captchaCode }}
            </div>
          </div>
        </div>
        
        <button 
          type="submit" 
          :disabled="loading"
          class="w-full btn-primary mt-6"
        >
          <i class="fas fa-sign-in-alt mr-2"></i>
          {{ loading ? '登录中...' : '登录' }}
        </button>
      </form>
      
      <div class="text-center mt-6 space-y-4">
        <div>
          <button @click="goToRegister" class="text-purple-400 hover:text-purple-300 text-sm">
            还没有账户？立即注册
          </button>
        </div>
        <div>
          <button class="text-gray-400 hover:text-gray-300 text-sm">
            忘记密码？
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import StatusBar from '@/components/StatusBar.vue'
import TopNav from '@/components/TopNav.vue'
import { mapActions } from 'vuex'

export default {
  name: 'Login',
  components: {
    StatusBar,
    TopNav
  },
  data() {
    return {
      loading: false,
      loginForm: {
        username: '',
        password: '',
        captcha: ''
      },
      captchaCode: 'A8K9'
    }
  },
  mounted() {
    this.refreshCaptcha()
  },
  methods: {
    ...mapActions('user', ['login']),
    
    async handleLogin() {
      if (this.loginForm.captcha.toUpperCase() !== this.captchaCode) {
        alert('验证码错误')
        this.refreshCaptcha()
        return
      }
      
      this.loading = true
      try {
        const result = await this.login({
          username: this.loginForm.username,
          password: this.loginForm.password
        })
        
        if (result.success) {
          alert('登录成功！')
          this.$router.push('/home')
        } else {
          alert(result.message || '登录失败')
          this.refreshCaptcha()
        }
      } catch (error) {
        console.error('登录错误:', error)
        alert('登录失败，请稍后重试')
        this.refreshCaptcha()
      } finally {
        this.loading = false
      }
    },
    
    refreshCaptcha() {
      // 生成随机验证码
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
      let result = ''
      for (let i = 0; i < 4; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length))
      }
      this.captchaCode = result
      this.loginForm.captcha = ''
    },
    
    goToRegister() {
      this.$router.push('/register')
    }
  }
}
</script>

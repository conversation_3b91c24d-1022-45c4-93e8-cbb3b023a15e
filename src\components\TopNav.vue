<template>
  <div class="top-nav">
    <div v-if="showBack" @click="goBack" class="cursor-pointer">
      <i class="fas fa-arrow-left"></i>
    </div>
    <div v-else class="logo">{{ title || 'JANS' }}</div>
    
    <div class="user-status">
      <template v-if="isLoggedIn">
        <span class="text-sm">{{ vipLevelText }}</span>
      </template>
      <template v-else>
        <button @click="goToLogin" class="text-sm bg-white bg-opacity-20 px-3 py-1 rounded-full">
          登录
        </button>
      </template>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'TopNav',
  props: {
    title: {
      type: String,
      default: ''
    },
    showBack: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapGetters('user', ['isLoggedIn', 'vipLevelText'])
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    goToLogin() {
      this.$router.push('/login')
    }
  }
}
</script>

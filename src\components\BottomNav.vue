<template>
  <div class="bottom-nav">
    <div 
      v-for="item in navItems" 
      :key="item.name"
      :class="['nav-item', { active: $route.name === item.route }]"
      @click="navigateTo(item.route)"
    >
      <span>{{ item.name }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BottomNav',
  data() {
    return {
      navItems: [
        { name: '首页', route: 'Home' },
        { name: '模特', route: 'Models' },
        { name: '热榜', route: 'Ranking' },
        { name: '我的', route: 'Profile' }
      ]
    }
  },
  methods: {
    navigateTo(route) {
      if (this.$route.name !== route) {
        this.$router.push({ name: route })
      }
    }
  }
}
</script>

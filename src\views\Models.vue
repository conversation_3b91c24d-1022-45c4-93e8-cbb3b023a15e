<template>
  <div class="app-container">
    <StatusBar />
    
    <!-- 主要内容 -->
    <div class="main-content">
      <!-- 头部 -->
      <div class="flex items-center justify-between p-4">
        <h1 class="text-2xl font-bold bg-gradient-to-r from-pink-400 to-purple-400 bg-clip-text text-transparent">时尚模特</h1>
        <div class="flex items-center space-x-3">
          <i @click="openSearch" class="fas fa-search text-xl text-gray-300 cursor-pointer"></i>
          <i @click="openFilter" class="fas fa-filter text-xl text-gray-300 cursor-pointer"></i>
        </div>
      </div>

      <!-- 分类标签 -->
      <div class="flex space-x-3 px-4 mb-6 overflow-x-auto">
        <div 
          v-for="category in categories" 
          :key="category.id"
          :class="['category-tag', { active: selectedCategory === category.id }]"
          @click="selectCategory(category.id)"
        >
          {{ category.name }}
        </div>
      </div>

      <!-- 模特列表 -->
      <div class="px-4">
        <div class="grid grid-cols-2 gap-4">
          <div 
            v-for="model in filteredModels" 
            :key="model.id"
            class="model-card" 
            @click="goToModelDetail(model.id)"
          >
            <div class="relative">
              <img 
                :src="model.avatar" 
                :alt="model.name" 
                class="w-full h-48 object-cover rounded-t-xl"
                @error="handleImageError"
              >
              <div v-if="model.isHot" class="absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                HOT
              </div>
              <div class="absolute bottom-2 left-2 bg-black/50 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-full">
                <i class="fas fa-heart text-red-400 mr-1"></i>
                {{ formatLikes(model.likes) }}
              </div>
            </div>
            <div class="p-4">
              <h3 class="font-bold text-white text-base mb-1">{{ model.name }}</h3>
              <p class="text-purple-300 text-sm mb-2">{{ model.category }} · {{ model.style }}</p>
              <div class="flex items-center justify-between">
                <div class="flex text-yellow-400 text-xs">
                  <i v-for="n in 5" :key="n" :class="getStarClass(model.rating, n)"></i>
                </div>
                <span class="text-gray-400 text-xs">{{ model.rating }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 加载更多 -->
        <div v-if="hasMore" class="text-center mt-6">
          <button @click="loadMore" :disabled="loading" class="btn-primary">
            {{ loading ? '加载中...' : '加载更多' }}
          </button>
        </div>
      </div>
    </div>

    <BottomNav />
  </div>
</template>

<script>
import StatusBar from '@/components/StatusBar.vue'
import BottomNav from '@/components/BottomNav.vue'
import { contentApi } from '@/api/modules/content'

export default {
  name: 'Models',
  components: {
    StatusBar,
    BottomNav
  },
  data() {
    return {
      loading: false,
      hasMore: true,
      page: 1,
      selectedCategory: 0,
      categories: [
        { id: 0, name: '全部' },
        { id: 1, name: '时装' },
        { id: 2, name: '内衣' },
        { id: 3, name: '泳装' },
        { id: 4, name: '古风' },
        { id: 5, name: '欧美' }
      ],
      models: [
        {
          id: 1,
          name: '莉莉·詹姆斯',
          category: '时装模特',
          style: '欧美风格',
          avatar: 'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=200&h=250&fit=crop&crop=face',
          likes: 12500,
          rating: 4.8,
          isHot: true,
          categoryId: 1
        },
        {
          id: 2,
          name: '艾玛·沃森',
          category: '时装模特',
          style: '知性优雅',
          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=200&h=250&fit=crop&crop=face',
          likes: 9800,
          rating: 4.6,
          isHot: false,
          categoryId: 1
        },
        {
          id: 3,
          name: '索菲亚·维加拉',
          category: '内衣模特',
          style: '性感魅力',
          avatar: 'https://images.unsplash.com/photo-1488426862026-3ee34a7d66df?w=200&h=250&fit=crop&crop=face',
          likes: 15600,
          rating: 4.9,
          isHot: true,
          categoryId: 2
        },
        {
          id: 4,
          name: '娜塔莉·波特曼',
          category: '艺术模特',
          style: '文艺清新',
          avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=200&h=250&fit=crop&crop=face',
          likes: 8900,
          rating: 4.7,
          isHot: false,
          categoryId: 4
        },
        {
          id: 5,
          name: '斯嘉丽·约翰逊',
          category: '时装模特',
          style: '现代时尚',
          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=200&h=250&fit=crop&crop=face',
          likes: 11200,
          rating: 4.5,
          isHot: false,
          categoryId: 1
        },
        {
          id: 6,
          name: '安妮·海瑟薇',
          category: '古风模特',
          style: '古典美人',
          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200&h=250&fit=crop&crop=face',
          likes: 7800,
          rating: 4.7,
          isHot: false,
          categoryId: 4
        }
      ]
    }
  },
  computed: {
    filteredModels() {
      if (this.selectedCategory === 0) {
        return this.models
      }
      return this.models.filter(model => model.categoryId === this.selectedCategory)
    }
  },
  async mounted() {
    await this.loadModels()
  },
  methods: {
    async loadModels() {
      try {
        this.loading = true
        const params = {
          page: this.page,
          category: this.selectedCategory
        }
        const data = await contentApi.getModels(params)
        if (data && data.success) {
          if (this.page === 1) {
            this.models = data.models
          } else {
            this.models.push(...data.models)
          }
          this.hasMore = data.hasMore
        }
      } catch (error) {
        console.error('加载模特数据失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    async selectCategory(categoryId) {
      this.selectedCategory = categoryId
      this.page = 1
      this.hasMore = true
      await this.loadModels()
    },
    
    async loadMore() {
      this.page++
      await this.loadModels()
    },
    
    goToModelDetail(id) {
      this.$router.push(`/model-detail/${id}`)
    },
    
    openSearch() {
      this.$router.push('/search-results')
    },
    
    openFilter() {
      alert('筛选功能开发中...')
    },
    
    formatLikes(likes) {
      if (likes >= 1000) {
        return (likes / 1000).toFixed(1) + 'K'
      }
      return likes.toString()
    },
    
    getStarClass(rating, index) {
      const fullStars = Math.floor(rating)
      const hasHalfStar = rating - fullStars >= 0.5
      
      if (index <= fullStars) {
        return 'fas fa-star'
      } else if (index === fullStars + 1 && hasHalfStar) {
        return 'fas fa-star-half-alt'
      } else {
        return 'far fa-star'
      }
    },
    
    handleImageError(event) {
      // 图片加载失败时显示默认图片
      event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjI1MCIgdmlld0JveD0iMCAwIDIwMCAyNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjUwIiBmaWxsPSIjNjY2NmVhIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iMTI1IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZG9taW5hbnQtYmFzZWxpbmU9Im1pZGRsZSIgZm9udC1zaXplPSIxNiI+5qih54m55aS05YOP</dGV4dD4KPC9zdmc+'
    }
  }
}
</script>

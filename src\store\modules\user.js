const state = {
  isLoggedIn: false,
  userInfo: null,
  isVip: false,
  vipLevel: 0, // 0: 普通用户, 1: VIP, 2: 超级VIP
  balance: 0
}

const mutations = {
  SET_LOGIN_STATUS(state, status) {
    state.isLoggedIn = status
  },
  SET_USER_INFO(state, userInfo) {
    state.userInfo = userInfo
  },
  SET_VIP_STATUS(state, { isVip, level }) {
    state.isVip = isVip
    state.vipLevel = level
  },
  SET_BALANCE(state, balance) {
    state.balance = balance
  }
}

const actions = {
  // 登录
  async login({ commit }, { username, password }) {
    try {
      // 这里应该调用实际的登录API
      const response = await fetch('/api/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ username, password })
      })
      
      if (response.ok) {
        const data = await response.json()
        commit('SET_LOGIN_STATUS', true)
        commit('SET_USER_INFO', data.user)
        commit('SET_VIP_STATUS', { isVip: data.user.isVip, level: data.user.vipLevel })
        commit('SET_BALANCE', data.user.balance)
        return { success: true, data }
      } else {
        return { success: false, message: '登录失败' }
      }
    } catch (error) {
      console.error('登录错误:', error)
      return { success: false, message: '网络错误' }
    }
  },
  
  // 注册
  async register({ commit }, userData) {
    try {
      const response = await fetch('/api/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(userData)
      })
      
      if (response.ok) {
        const data = await response.json()
        return { success: true, data }
      } else {
        return { success: false, message: '注册失败' }
      }
    } catch (error) {
      console.error('注册错误:', error)
      return { success: false, message: '网络错误' }
    }
  },
  
  // 登出
  logout({ commit }) {
    commit('SET_LOGIN_STATUS', false)
    commit('SET_USER_INFO', null)
    commit('SET_VIP_STATUS', { isVip: false, level: 0 })
    commit('SET_BALANCE', 0)
  },
  
  // 充值
  async recharge({ commit }, amount) {
    try {
      const response = await fetch('/api/recharge', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ amount })
      })
      
      if (response.ok) {
        const data = await response.json()
        commit('SET_BALANCE', data.balance)
        return { success: true, data }
      } else {
        return { success: false, message: '充值失败' }
      }
    } catch (error) {
      console.error('充值错误:', error)
      return { success: false, message: '网络错误' }
    }
  }
}

const getters = {
  isLoggedIn: state => state.isLoggedIn,
  userInfo: state => state.userInfo,
  isVip: state => state.isVip,
  vipLevel: state => state.vipLevel,
  balance: state => state.balance,
  vipLevelText: state => {
    switch (state.vipLevel) {
      case 0: return '普通用户'
      case 1: return 'VIP会员'
      case 2: return '超级VIP'
      default: return '普通用户'
    }
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}

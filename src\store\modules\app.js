const state = {
  loading: false,
  currentTime: '9:41'
}

const mutations = {
  SET_LOADING(state, loading) {
    state.loading = loading
  },
  SET_CURRENT_TIME(state, time) {
    state.currentTime = time
  }
}

const actions = {
  setLoading({ commit }, loading) {
    commit('SET_LOADING', loading)
  },
  updateTime({ commit }) {
    const now = new Date()
    const time = `${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}`
    commit('SET_CURRENT_TIME', time)
  }
}

const getters = {
  loading: state => state.loading,
  currentTime: state => state.currentTime
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}

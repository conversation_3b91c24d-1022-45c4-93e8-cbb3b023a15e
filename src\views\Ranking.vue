<template>
  <div class="app-container">
    <StatusBar />
    
    <!-- 主要内容 -->
    <div class="main-content">
      <!-- 头部 -->
      <div class="flex items-center justify-between p-4">
        <h1 class="text-2xl font-bold bg-gradient-to-r from-yellow-400 to-red-400 bg-clip-text text-transparent">热门排行</h1>
        <div class="flex items-center space-x-3">
          <i class="fas fa-trophy text-xl text-yellow-400"></i>
        </div>
      </div>

      <!-- 排行榜类型 -->
      <div class="flex space-x-3 px-4 mb-6 overflow-x-auto">
        <div 
          v-for="type in rankingTypes" 
          :key="type.id"
          :class="['category-tag', { active: selectedType === type.id }]"
          @click="selectType(type.id)"
        >
          {{ type.name }}
        </div>
      </div>

      <!-- 排行榜列表 -->
      <div class="px-4">
        <div class="space-y-4">
          <div 
            v-for="(item, index) in rankingList" 
            :key="item.id"
            class="card cursor-pointer hover:bg-opacity-20 transition-all"
            @click="goToDetail(item)"
          >
            <div class="flex items-center space-x-4">
              <!-- 排名 -->
              <div :class="getRankClass(index + 1)" class="w-8 h-8 rounded-full flex items-center justify-center font-bold text-sm">
                {{ index + 1 }}
              </div>
              
              <!-- 内容图片 -->
              <div class="w-16 h-16 rounded-lg overflow-hidden">
                <img 
                  :src="item.cover" 
                  :alt="item.title"
                  class="w-full h-full object-cover"
                  @error="handleImageError"
                >
              </div>
              
              <!-- 内容信息 -->
              <div class="flex-1">
                <h3 class="text-white font-semibold text-base mb-1">{{ item.title }}</h3>
                <p class="text-gray-400 text-sm mb-2">{{ item.description }}</p>
                <div class="flex items-center space-x-4 text-xs text-gray-500">
                  <span><i class="fas fa-eye mr-1"></i>{{ formatNumber(item.views) }}</span>
                  <span><i class="fas fa-heart mr-1"></i>{{ formatNumber(item.likes) }}</span>
                  <span><i class="fas fa-comment mr-1"></i>{{ formatNumber(item.comments) }}</span>
                </div>
              </div>
              
              <!-- 热度值 -->
              <div class="text-right">
                <div class="text-yellow-400 font-bold text-lg">{{ item.hotScore }}</div>
                <div class="text-gray-500 text-xs">热度</div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 加载更多 -->
        <div v-if="hasMore" class="text-center mt-6">
          <button @click="loadMore" :disabled="loading" class="btn-primary">
            {{ loading ? '加载中...' : '加载更多' }}
          </button>
        </div>
      </div>
    </div>

    <BottomNav />
  </div>
</template>

<script>
import StatusBar from '@/components/StatusBar.vue'
import BottomNav from '@/components/BottomNav.vue'
import { contentApi } from '@/api/modules/content'

export default {
  name: 'Ranking',
  components: {
    StatusBar,
    BottomNav
  },
  data() {
    return {
      loading: false,
      hasMore: true,
      page: 1,
      selectedType: 'hot',
      rankingTypes: [
        { id: 'hot', name: '热门' },
        { id: 'new', name: '最新' },
        { id: 'model', name: '模特' },
        { id: 'magazine', name: '杂志' },
        { id: 'community', name: '社区' }
      ],
      rankingList: [
        {
          id: 1,
          title: '时尚前沿第88期',
          description: '春季时装周特辑',
          cover: 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=100&h=100&fit=crop',
          views: 125000,
          likes: 8900,
          comments: 456,
          hotScore: 9.8,
          type: 'magazine'
        },
        {
          id: 2,
          title: '莉莉·詹姆斯',
          description: '欧美时装模特',
          cover: 'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=100&h=100&fit=crop&crop=face',
          views: 98000,
          likes: 12500,
          comments: 789,
          hotScore: 9.6,
          type: 'model'
        },
        {
          id: 3,
          title: '夏日泳装特辑',
          description: '清凉一夏，展现完美身材',
          cover: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=100&h=100&fit=crop',
          views: 87000,
          likes: 6700,
          comments: 234,
          hotScore: 9.4,
          type: 'magazine'
        },
        {
          id: 4,
          title: '古风摄影技巧分享',
          description: '如何拍出唯美古风照片',
          cover: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=100&h=100&fit=crop',
          views: 76000,
          likes: 5400,
          comments: 567,
          hotScore: 9.2,
          type: 'community'
        },
        {
          id: 5,
          title: '艾玛·沃森',
          description: '知性优雅代表',
          cover: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
          views: 65000,
          likes: 9800,
          comments: 345,
          hotScore: 9.0,
          type: 'model'
        }
      ]
    }
  },
  async mounted() {
    await this.loadRankingData()
  },
  methods: {
    async loadRankingData() {
      try {
        this.loading = true
        const data = await contentApi.getRankingData(this.selectedType)
        if (data && data.success) {
          if (this.page === 1) {
            this.rankingList = data.list
          } else {
            this.rankingList.push(...data.list)
          }
          this.hasMore = data.hasMore
        }
      } catch (error) {
        console.error('加载排行榜数据失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    async selectType(typeId) {
      this.selectedType = typeId
      this.page = 1
      this.hasMore = true
      await this.loadRankingData()
    },
    
    async loadMore() {
      this.page++
      await this.loadRankingData()
    },
    
    goToDetail(item) {
      if (item.type === 'model') {
        this.$router.push(`/model-detail/${item.id}`)
      } else if (item.type === 'magazine') {
        this.$router.push(`/magazine-detail/${item.id}`)
      } else if (item.type === 'community') {
        this.$router.push(`/post-detail/${item.id}`)
      }
    },
    
    getRankClass(rank) {
      if (rank === 1) {
        return 'bg-gradient-to-br from-yellow-400 to-yellow-600 text-white'
      } else if (rank === 2) {
        return 'bg-gradient-to-br from-gray-300 to-gray-500 text-white'
      } else if (rank === 3) {
        return 'bg-gradient-to-br from-yellow-600 to-yellow-800 text-white'
      } else {
        return 'bg-gray-600 text-white'
      }
    },
    
    formatNumber(num) {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    },
    
    handleImageError(event) {
      event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjNjY2NmVhIi8+Cjx0ZXh0IHg9IjUwIiB5PSI1MCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiIGZvbnQtc2l6ZT0iMTIiPuaXoOWbvueJhzwvdGV4dD4KPC9zdmc+'
    }
  }
}
</script>

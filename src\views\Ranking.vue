<template>
  <div class="app-container">
    <StatusBar />
    <TopNav />

    <!-- 内容区域 -->
    <div class="content">
      <!-- 排行榜类型选择 -->
      <div class="mb-6">
        <div class="flex bg-gray-800 rounded-lg p-1">
          <button
            v-for="type in rankingTypes"
            :key="type.id"
            @click="selectType(type.id)"
            :class="[
              'flex-1 py-2 px-3 rounded-md font-semibold transition-all text-sm',
              selectedType === type.id ? 'bg-purple-600 text-white' : 'text-gray-400'
            ]"
          >
            {{ type.name }}
          </button>
        </div>
      </div>

      <!-- 时间周期选择 -->
      <div class="mb-6">
        <div class="flex bg-gray-800 rounded-lg p-1">
          <button
            v-for="period in periodTypes"
            :key="period.id"
            @click="selectPeriod(period.id)"
            :class="[
              'flex-1 py-2 px-4 rounded-md font-semibold transition-all',
              selectedPeriod === period.id ? 'bg-blue-600 text-white' : 'text-gray-400'
            ]"
          >
            {{ period.name }}
          </button>
        </div>
      </div>

      <!-- 排行榜内容 -->
      <div id="rankingContent">
        <!-- 前三名特殊显示 -->
        <div class="grid grid-cols-3 gap-4 mb-6">
          <!-- 第二名 -->
          <div class="text-center">
            <div class="relative mb-2">
              <div class="w-16 h-20 bg-gradient-to-br from-gray-400 to-gray-600 rounded-lg mx-auto flex items-center justify-center">
                <i class="fas fa-image text-white"></i>
              </div>
              <div class="absolute -top-2 -right-2 w-6 h-6 bg-gray-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                2
              </div>
            </div>
            <p class="text-white text-xs font-semibold">第154期</p>
            <p class="text-gray-400 text-xs">1.8k</p>
          </div>

          <!-- 第一名 -->
          <div class="text-center">
            <div class="relative mb-2">
              <div class="w-20 h-24 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg mx-auto flex items-center justify-center">
                <i class="fas fa-crown text-white text-xl"></i>
              </div>
              <div class="absolute -top-2 -right-2 w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                1
              </div>
            </div>
            <p class="text-white text-xs font-semibold">第155期</p>
            <p class="text-gray-400 text-xs">2.1k</p>
          </div>

          <!-- 第三名 -->
          <div class="text-center">
            <div class="relative mb-2">
              <div class="w-16 h-20 bg-gradient-to-br from-orange-400 to-red-500 rounded-lg mx-auto flex items-center justify-center">
                <i class="fas fa-image text-white"></i>
              </div>
              <div class="absolute -top-2 -right-2 w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                3
              </div>
            </div>
            <p class="text-white text-xs font-semibold">第153期</p>
            <p class="text-gray-400 text-xs">1.6k</p>
          </div>
        </div>

        <!-- 4-10名列表 -->
        <div class="space-y-3">
          <div
            v-for="(item, index) in rankingList"
            :key="item.id"
            @click="goToDetail(item)"
            class="card cursor-pointer hover:bg-opacity-20 transition-all"
          >
            <div class="flex items-center space-x-4">
              <div class="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                {{ index + 4 }}
              </div>
              <div :class="['w-12 h-16 rounded flex items-center justify-center', item.bgClass]">
                <i class="fas fa-image text-white text-sm"></i>
              </div>
              <div class="flex-1">
                <p class="text-white font-semibold text-sm">{{ item.title }}</p>
                <p class="text-gray-400 text-xs">{{ item.views }} 浏览</p>
              </div>
              <div :class="['text-sm', item.changeClass]">
                <i :class="['mr-1', item.changeIcon]"></i>
                {{ item.change }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <BottomNav />
  </div>
</template>

<script>
import StatusBar from '@/components/StatusBar.vue'
import TopNav from '@/components/TopNav.vue'
import BottomNav from '@/components/BottomNav.vue'
import { contentApi } from '@/api/modules/content'

export default {
  name: 'Ranking',
  components: {
    StatusBar,
    TopNav,
    BottomNav
  },
  data() {
    return {
      loading: false,
      hasMore: true,
      page: 1,
      selectedType: 'total',
      selectedPeriod: 'total',
      rankingTypes: [
        { id: 'total', name: '总榜' },
        { id: 'fashion', name: '时尚前沿' },
        { id: 'star', name: '明星写真' }
      ],
      periodTypes: [
        { id: 'total', name: '总榜' },
        { id: 'month', name: '月榜' },
        { id: 'quarter', name: '季度榜' }
      ],
      rankingList: [
        {
          id: 4,
          title: '第152期 · 艾米丽 · 冬日暖阳',
          views: '1.4k',
          bgClass: 'bg-gradient-to-br from-purple-500 to-pink-500',
          change: '+2',
          changeClass: 'text-green-400',
          changeIcon: 'fas fa-arrow-up',
          type: 'magazine'
        },
        {
          id: 5,
          title: '第151期 · 维多利亚 · 复古风情',
          views: '1.3k',
          bgClass: 'bg-gradient-to-br from-blue-500 to-purple-500',
          change: '0',
          changeClass: 'text-gray-400',
          changeIcon: 'fas fa-minus',
          type: 'magazine'
        },
        {
          id: 6,
          title: '第150期 · 安娜 · 都市丽人',
          views: '1.2k',
          bgClass: 'bg-gradient-to-br from-green-500 to-blue-500',
          change: '-1',
          changeClass: 'text-red-400',
          changeIcon: 'fas fa-arrow-down',
          type: 'magazine'
        },
        {
          id: 7,
          title: '第149期 · 莉莉 · 春日花语',
          views: '1.1k',
          bgClass: 'bg-gradient-to-br from-red-500 to-pink-500',
          change: '+3',
          changeClass: 'text-green-400',
          changeIcon: 'fas fa-arrow-up',
          type: 'magazine'
        },
        {
          id: 8,
          title: '第148期 · 凯特 · 夏日海滩',
          views: '1.0k',
          bgClass: 'bg-gradient-to-br from-yellow-500 to-orange-500',
          change: '0',
          changeClass: 'text-gray-400',
          changeIcon: 'fas fa-minus',
          type: 'magazine'
        },
        {
          id: 9,
          title: '第147期 · 娜塔莎 · 秋日私语',
          views: '950',
          bgClass: 'bg-gradient-to-br from-indigo-500 to-purple-500',
          change: '-2',
          changeClass: 'text-red-400',
          changeIcon: 'fas fa-arrow-down',
          type: 'magazine'
        },
        {
          id: 10,
          title: '第146期 · 伊莎贝拉 · 优雅晚装',
          views: '890',
          bgClass: 'bg-gradient-to-br from-pink-500 to-red-500',
          change: '+1',
          changeClass: 'text-green-400',
          changeIcon: 'fas fa-arrow-up',
          type: 'magazine'
        }
      ]
    }
  },
  async mounted() {
    await this.loadRankingData()
  },
  methods: {
    async loadRankingData() {
      try {
        this.loading = true
        const data = await contentApi.getRankingData({
          type: this.selectedType,
          period: this.selectedPeriod
        })
        if (data && data.success) {
          if (this.page === 1) {
            this.rankingList = data.list
          } else {
            this.rankingList.push(...data.list)
          }
          this.hasMore = data.hasMore
        }
      } catch (error) {
        console.error('加载排行榜数据失败:', error)
      } finally {
        this.loading = false
      }
    },

    async selectType(typeId) {
      this.selectedType = typeId
      this.page = 1
      this.hasMore = true
      await this.loadRankingData()
    },

    async selectPeriod(periodId) {
      this.selectedPeriod = periodId
      this.page = 1
      this.hasMore = true
      await this.loadRankingData()
    },

    async loadMore() {
      this.page++
      await this.loadRankingData()
    },

    goToDetail(item) {
      if (item.type === 'model') {
        this.$router.push(`/model-detail/${item.id}`)
      } else if (item.type === 'magazine') {
        this.$router.push(`/magazine-detail/${item.id}`)
      } else if (item.type === 'community') {
        this.$router.push(`/post-detail/${item.id}`)
      }
    }
  }
}
</script>

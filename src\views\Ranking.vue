<template>
  <div class="app-container">
    <StatusBar />
    <TopNav />

    <!-- 内容区域 -->
    <div class="content">
      <!-- 排行榜类型选择 -->
      <div class="mb-6">
        <div class="flex bg-gray-800 rounded-lg p-1">
          <button
            v-for="type in rankingTypes"
            :key="type.id"
            @click="selectType(type.id)"
            :class="[
              'flex-1 py-2 px-3 rounded-md font-semibold transition-all text-sm',
              selectedType === type.id ? 'bg-purple-600 text-white' : 'text-gray-400'
            ]"
          >
            {{ type.name }}
          </button>
        </div>
      </div>

      <!-- 排行榜标题 -->
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-xl font-bold text-white flex items-center">
          <i class="fas fa-trophy mr-2 text-yellow-400"></i>
          {{ getCurrentTypeTitle() }}
        </h2>
        <span class="text-gray-400 text-sm">实时更新</span>
      </div>

      <!-- 排行榜内容 -->
      <div id="rankingContent">
        <!-- 前三名特殊显示 -->
        <div class="grid grid-cols-3 gap-4 mb-6">
          <!-- 第二名 -->
          <div class="text-center">
            <div class="relative mb-2">
              <div class="w-16 h-20 bg-gradient-to-br from-gray-400 to-gray-600 rounded-lg mx-auto flex items-center justify-center">
                <i class="fas fa-image text-white"></i>
              </div>
              <div class="absolute -top-2 -right-2 w-6 h-6 bg-gray-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                2
              </div>
            </div>
            <p class="text-white text-xs font-semibold">{{ topThree[1] ? topThree[1].title : '第154期' }}</p>
            <p class="text-gray-400 text-xs">{{ topThree[1] ? formatNumber(topThree[1].views) : '1.8k' }}</p>
          </div>

          <!-- 第一名 -->
          <div class="text-center">
            <div class="relative mb-2">
              <div class="w-20 h-24 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-lg mx-auto flex items-center justify-center">
                <i class="fas fa-crown text-white text-xl"></i>
              </div>
              <div class="absolute -top-2 -right-2 w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                1
              </div>
            </div>
            <p class="text-white text-sm font-bold">{{ topThree[0] ? topThree[0].title : '第155期' }}</p>
            <p class="text-yellow-400 text-sm font-semibold">{{ topThree[0] ? formatNumber(topThree[0].views) : '2.3k' }}</p>
          </div>

          <!-- 第三名 -->
          <div class="text-center">
            <div class="relative mb-2">
              <div class="w-16 h-20 bg-gradient-to-br from-yellow-600 to-yellow-800 rounded-lg mx-auto flex items-center justify-center">
                <i class="fas fa-medal text-white"></i>
              </div>
              <div class="absolute -top-2 -right-2 w-6 h-6 bg-yellow-700 rounded-full flex items-center justify-center text-white text-xs font-bold">
                3
              </div>
            </div>
            <p class="text-white text-xs font-semibold">{{ topThree[2] ? topThree[2].title : '第153期' }}</p>
            <p class="text-gray-400 text-xs">{{ topThree[2] ? formatNumber(topThree[2].views) : '1.5k' }}</p>
          </div>
        </div>

        <!-- 4-10名列表 -->
        <div class="space-y-3">
          <div
            v-for="(item, index) in remainingList"
            :key="item.id"
            @click="goToDetail(item)"
            class="card cursor-pointer hover:bg-opacity-20 transition-all"
          >
            <div class="flex items-center space-x-4">
              <!-- 排名 -->
              <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                {{ index + 4 }}
              </div>

              <!-- 内容图片 -->
              <div class="w-12 h-12 bg-gradient-to-br from-purple-600 to-pink-600 rounded-lg flex items-center justify-center">
                <i class="fas fa-image text-white"></i>
              </div>

              <!-- 内容信息 -->
              <div class="flex-1">
                <h3 class="text-white font-semibold text-sm mb-1">{{ item.title }}</h3>
                <p class="text-gray-400 text-xs">{{ item.description }}</p>
              </div>

              <!-- 数据 -->
              <div class="text-right">
                <div class="text-white text-sm font-semibold">{{ formatNumber(item.views) }}</div>
                <div class="text-gray-500 text-xs">观看</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 加载更多 -->
        <div v-if="hasMore" class="text-center mt-6">
          <button @click="loadMore" :disabled="loading" class="btn-primary">
            {{ loading ? '加载中...' : '查看更多' }}
          </button>
        </div>
      </div>
    </div>

    <BottomNav />
  </div>
</template>

<script>
import StatusBar from '@/components/StatusBar.vue'
import TopNav from '@/components/TopNav.vue'
import BottomNav from '@/components/BottomNav.vue'
import { contentApi } from '@/api/modules/content'

export default {
  name: 'Ranking',
  components: {
    StatusBar,
    TopNav,
    BottomNav
  },
  data() {
    return {
      loading: false,
      hasMore: true,
      page: 1,
      selectedType: 'total',
      rankingTypes: [
        { id: 'total', name: '总榜' },
        { id: 'fashion', name: '时尚前沿' },
        { id: 'star', name: '明星写真' }
      ],
      rankingList: [
        {
          id: 1,
          title: '第155期',
          description: '春季时装周特辑',
          views: 2300,
          likes: 8900,
          comments: 456,
          hotScore: 9.8,
          type: 'magazine'
        },
        {
          id: 2,
          title: '第154期',
          description: '冬日暖阳写真',
          views: 1800,
          likes: 7200,
          comments: 389,
          hotScore: 9.6,
          type: 'magazine'
        },
        {
          id: 3,
          title: '第153期',
          description: '复古时尚回潮',
          views: 1500,
          likes: 6700,
          comments: 234,
          hotScore: 9.4,
          type: 'magazine'
        },
        {
          id: 4,
          title: '第152期',
          description: '街头时尚捕捉',
          views: 1200,
          likes: 5400,
          comments: 567,
          hotScore: 9.2,
          type: 'magazine'
        },
        {
          id: 5,
          title: '第151期',
          description: '艺术人像摄影',
          views: 1100,
          likes: 4800,
          comments: 345,
          hotScore: 9.0,
          type: 'magazine'
        },
        {
          id: 6,
          title: '第150期',
          description: '时尚大片精选',
          views: 980,
          likes: 4200,
          comments: 298,
          hotScore: 8.8,
          type: 'magazine'
        },
        {
          id: 7,
          title: '第149期',
          description: '模特写真集',
          views: 890,
          likes: 3900,
          comments: 267,
          hotScore: 8.6,
          type: 'magazine'
        }
      ]
    }
  },
  computed: {
    topThree() {
      return this.rankingList.slice(0, 3)
    },
    remainingList() {
      return this.rankingList.slice(3)
    }
  },
  async mounted() {
    await this.loadRankingData()
  },
  methods: {
    async loadRankingData() {
      try {
        this.loading = true
        const data = await contentApi.getRankingData(this.selectedType)
        if (data && data.success) {
          if (this.page === 1) {
            this.rankingList = data.list
          } else {
            this.rankingList.push(...data.list)
          }
          this.hasMore = data.hasMore
        }
      } catch (error) {
        console.error('加载排行榜数据失败:', error)
      } finally {
        this.loading = false
      }
    },

    async selectType(typeId) {
      this.selectedType = typeId
      this.page = 1
      this.hasMore = true
      await this.loadRankingData()
    },

    async loadMore() {
      this.page++
      await this.loadRankingData()
    },

    getCurrentTypeTitle() {
      const typeMap = {
        'total': '总排行榜',
        'fashion': '时尚前沿排行榜',
        'star': '明星写真排行榜'
      }
      return typeMap[this.selectedType] || '排行榜'
    },

    goToDetail(item) {
      if (item.type === 'model') {
        this.$router.push(`/model-detail/${item.id}`)
      } else if (item.type === 'magazine') {
        this.$router.push(`/magazine-detail/${item.id}`)
      } else if (item.type === 'community') {
        this.$router.push(`/post-detail/${item.id}`)
      }
    },

    formatNumber(num) {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k'
      }
      return num.toString()
    }
  }
}
</script>

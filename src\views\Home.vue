<template>
  <div class="app-container">
    <StatusBar />
    <TopNav />
    
    <!-- 内容区域 -->
    <div class="content">
      <!-- Banner轮播 -->
      <div class="relative mb-6 rounded-2xl overflow-hidden">
        <div class="h-48 bg-gradient-to-r from-purple-600 to-pink-600 flex items-center justify-center">
          <div class="text-center">
            <h2 class="text-3xl font-bold text-white mb-2">{{ bannerData.title }}</h2>
            <p class="text-white text-opacity-90">{{ bannerData.subtitle }}</p>
          </div>
        </div>
        <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
          <div 
            v-for="(item, index) in bannerData.indicators" 
            :key="index"
            :class="['w-2 h-2 rounded-full', index === 0 ? 'bg-white' : 'bg-white bg-opacity-50']"
          ></div>
        </div>
      </div>
      
      <!-- 功能按钮区 -->
      <div class="grid grid-cols-3 gap-4 mb-6">
        <div class="col-span-1 space-y-4">
          <button @click="goToVip" class="w-full h-20 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl flex flex-col items-center justify-center text-white font-semibold shadow-lg">
            <i class="fas fa-crown text-xl mb-1"></i>
            <span class="text-sm">VIP会员</span>
          </button>
          <button @click="goToCommunity" class="w-full h-20 bg-gradient-to-br from-green-400 to-blue-500 rounded-xl flex flex-col items-center justify-center text-white font-semibold shadow-lg">
            <i class="fas fa-comments text-xl mb-1"></i>
            <span class="text-sm">社区吐槽</span>
          </button>
        </div>
        <div class="col-span-2">
          <button @click="goToRecharge" class="w-full h-44 bg-gradient-to-br from-pink-500 to-purple-600 rounded-xl flex flex-col items-center justify-center text-white font-semibold shadow-lg">
            <i class="fas fa-wallet text-3xl mb-2"></i>
            <span class="text-lg">充值</span>
            <span class="text-sm opacity-80">快速充值享受更多服务</span>
          </button>
        </div>
      </div>
      
      <!-- 电子杂志区域 -->
      <div class="mb-6">
        <h3 class="text-xl font-bold text-white mb-4 flex items-center">
          <i class="fas fa-book-open mr-2 text-purple-400"></i>
          精选杂志
        </h3>
        <div class="grid grid-cols-3 gap-4">
          <div 
            v-for="magazine in magazines" 
            :key="magazine.id"
            @click="goToMagazineDetail(magazine.id)" 
            class="cursor-pointer"
          >
            <div :class="['h-32 rounded-lg mb-2 flex items-center justify-center', magazine.bgClass]">
              <i :class="['text-white text-2xl', magazine.icon]"></i>
            </div>
            <p class="text-white text-sm text-center">{{ magazine.title }}</p>
          </div>
        </div>
      </div>
    </div>
    
    <FloatingBtn />
    <BottomNav />
  </div>
</template>

<script>
import StatusBar from '@/components/StatusBar.vue'
import TopNav from '@/components/TopNav.vue'
import BottomNav from '@/components/BottomNav.vue'
import FloatingBtn from '@/components/FloatingBtn.vue'
import { contentApi } from '@/api/modules/content'

export default {
  name: 'Home',
  components: {
    StatusBar,
    TopNav,
    BottomNav,
    FloatingBtn
  },
  data() {
    return {
      bannerData: {
        title: '时尚先锋',
        subtitle: '探索时尚摄影的无限可能',
        indicators: [1, 2, 3]
      },
      magazines: [
        {
          id: 1,
          title: '时尚前沿',
          icon: 'fas fa-camera',
          bgClass: 'bg-gradient-to-br from-purple-600 to-pink-600'
        },
        {
          id: 2,
          title: '明星写真',
          icon: 'fas fa-star',
          bgClass: 'bg-gradient-to-br from-blue-600 to-purple-600'
        },
        {
          id: 3,
          title: '情感大片',
          icon: 'fas fa-heart',
          bgClass: 'bg-gradient-to-br from-green-600 to-blue-600'
        },
        {
          id: 4,
          title: '热门推荐',
          icon: 'fas fa-fire',
          bgClass: 'bg-gradient-to-br from-red-600 to-pink-600'
        },
        {
          id: 5,
          title: '奢华时尚',
          icon: 'fas fa-gem',
          bgClass: 'bg-gradient-to-br from-yellow-600 to-red-600'
        },
        {
          id: 6,
          title: '艺术摄影',
          icon: 'fas fa-magic',
          bgClass: 'bg-gradient-to-br from-indigo-600 to-purple-600'
        }
      ]
    }
  },
  async mounted() {
    await this.loadHomeData()
  },
  methods: {
    async loadHomeData() {
      try {
        this.$store.dispatch('app/setLoading', true)
        // 调用API获取首页数据
        const data = await contentApi.getHomeData()
        if (data && data.success) {
          // 更新页面数据
          if (data.banner) {
            this.bannerData = { ...this.bannerData, ...data.banner }
          }
          if (data.magazines) {
            this.magazines = data.magazines
          }
        }
      } catch (error) {
        console.error('加载首页数据失败:', error)
        // 使用默认数据
      } finally {
        this.$store.dispatch('app/setLoading', false)
      }
    },
    goToVip() {
      this.$router.push('/vip')
    },
    goToCommunity() {
      this.$router.push('/community')
    },
    goToRecharge() {
      this.$router.push('/recharge')
    },
    goToMagazineDetail(id) {
      this.$router.push(`/magazine-detail/${id}`)
    }
  }
}
</script>

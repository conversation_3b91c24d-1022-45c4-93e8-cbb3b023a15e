<template>
  <div class="app-container">
    <StatusBar />
    <TopNav title="注册" :show-back="true" />
    
    <!-- 内容区域 -->
    <div class="content">
      <div class="text-center mb-6">
        <div class="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full mx-auto mb-4 flex items-center justify-center">
          <i class="fas fa-user-plus text-white text-2xl"></i>
        </div>
        <h2 class="text-xl font-bold text-white mb-2">创建新账户</h2>
        <p class="text-gray-400">加入我们，开启精彩体验</p>
      </div>
      
      <form @submit.prevent="handleRegister" class="space-y-4">
        <div>
          <label class="block text-white text-sm font-medium mb-2">用户名</label>
          <input 
            v-model="registerForm.username"
            type="text" 
            class="input-field" 
            placeholder="请输入用户名"
            required
          >
        </div>
        
        <div>
          <label class="block text-white text-sm font-medium mb-2">密码</label>
          <input 
            v-model="registerForm.password"
            type="password" 
            class="input-field" 
            placeholder="请输入密码"
            required
          >
        </div>
        
        <div>
          <label class="block text-white text-sm font-medium mb-2">确认密码</label>
          <input 
            v-model="registerForm.confirmPassword"
            type="password" 
            class="input-field" 
            placeholder="请再次输入密码"
            required
          >
        </div>
        
        <div>
          <label class="block text-white text-sm font-medium mb-2">密码提示问题</label>
          <select v-model="registerForm.securityQuestion" class="input-field" required>
            <option value="">请选择密码提示问题</option>
            <option value="birthplace">您的出生地是？</option>
            <option value="pet">您的第一个宠物叫什么？</option>
            <option value="color">您最喜欢的颜色是？</option>
            <option value="school">您的小学校名是？</option>
          </select>
        </div>
        
        <div>
          <label class="block text-white text-sm font-medium mb-2">密码答案</label>
          <input 
            v-model="registerForm.securityAnswer"
            type="text" 
            class="input-field" 
            placeholder="请输入答案"
            required
          >
        </div>
        
        <div class="flex items-start space-x-3 mt-6">
          <input 
            v-model="registerForm.agreement"
            type="checkbox" 
            id="agreement" 
            class="mt-1"
            required
          >
          <label for="agreement" class="text-sm text-gray-300">
            我已阅读并同意 
            <button type="button" @click="showAgreement" class="text-purple-400 underline">
              《注册须知》
            </button>
          </label>
        </div>
        
        <button 
          type="submit" 
          :disabled="loading"
          class="w-full btn-primary mt-6"
        >
          <i class="fas fa-user-plus mr-2"></i>
          {{ loading ? '注册中...' : '注册' }}
        </button>
      </form>
      
      <div class="text-center mt-6">
        <span class="text-gray-400 text-sm">已有账户？</span>
        <button @click="goToLogin" class="text-purple-400 hover:text-purple-300 text-sm ml-1">
          立即登录
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import StatusBar from '@/components/StatusBar.vue'
import TopNav from '@/components/TopNav.vue'
import { mapActions } from 'vuex'

export default {
  name: 'Register',
  components: {
    StatusBar,
    TopNav
  },
  data() {
    return {
      loading: false,
      registerForm: {
        username: '',
        password: '',
        confirmPassword: '',
        securityQuestion: '',
        securityAnswer: '',
        agreement: false
      }
    }
  },
  methods: {
    ...mapActions('user', ['register']),
    
    async handleRegister() {
      // 验证密码
      if (this.registerForm.password !== this.registerForm.confirmPassword) {
        alert('两次输入的密码不一致')
        return
      }
      
      if (this.registerForm.password.length < 6) {
        alert('密码长度至少6位')
        return
      }
      
      if (!this.registerForm.agreement) {
        alert('请先同意注册须知')
        return
      }
      
      this.loading = true
      try {
        const result = await this.register({
          username: this.registerForm.username,
          password: this.registerForm.password,
          securityQuestion: this.registerForm.securityQuestion,
          securityAnswer: this.registerForm.securityAnswer
        })
        
        if (result.success) {
          alert('注册成功！请登录')
          this.$router.push('/login')
        } else {
          alert(result.message || '注册失败')
        }
      } catch (error) {
        console.error('注册错误:', error)
        alert('注册失败，请稍后重试')
      } finally {
        this.loading = false
      }
    },
    
    showAgreement() {
      alert(`注册须知：

1. 用户必须年满18周岁
2. 禁止发布违法违规内容
3. 尊重他人隐私和版权
4. 遵守平台使用规范
5. 账户安全由用户自行负责

详细条款请查看用户协议。`)
    },
    
    goToLogin() {
      this.$router.push('/login')
    }
  }
}
</script>

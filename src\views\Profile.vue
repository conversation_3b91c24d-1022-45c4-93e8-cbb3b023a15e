<template>
  <div class="app-container">
    <StatusBar />
    
    <!-- 主要内容 -->
    <div class="main-content">
      <!-- 用户信息卡片 -->
      <div class="card mb-6">
        <div class="flex items-center space-x-4">
          <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
            <i v-if="!isLoggedIn" class="fas fa-user text-white text-2xl"></i>
            <img v-else-if="userInfo && userInfo.avatar" :src="userInfo.avatar" alt="头像" class="w-full h-full rounded-full object-cover">
            <i v-else class="fas fa-user text-white text-2xl"></i>
          </div>
          <div class="flex-1">
            <h3 class="text-white font-bold text-lg">{{ displayName }}</h3>
            <p class="text-gray-400 text-sm">{{ vipLevelText }}</p>
            <p v-if="isLoggedIn" class="text-purple-300 text-sm">余额：¥{{ balance }}</p>
          </div>
          <div v-if="!isLoggedIn">
            <button @click="goToLogin" class="btn-primary text-sm">
              登录
            </button>
          </div>
        </div>
      </div>
      
      <!-- 快捷功能 -->
      <div class="grid grid-cols-4 gap-4 mb-6">
        <div 
          v-for="shortcut in shortcuts" 
          :key="shortcut.id"
          @click="handleShortcut(shortcut.action)"
          class="text-center cursor-pointer"
        >
          <div :class="['w-12 h-12 rounded-xl mx-auto mb-2 flex items-center justify-center', shortcut.bgClass]">
            <i :class="['text-white text-lg', shortcut.icon]"></i>
          </div>
          <span class="text-white text-xs">{{ shortcut.name }}</span>
        </div>
      </div>
      
      <!-- 菜单列表 -->
      <div class="space-y-1">
        <div 
          v-for="menu in menuItems" 
          :key="menu.id"
          @click="handleMenu(menu.action)"
          class="menu-item"
        >
          <div class="flex items-center">
            <i :class="['text-purple-400', menu.icon]"></i>
            <span>{{ menu.name }}</span>
          </div>
          <i class="fas fa-chevron-right text-gray-500"></i>
        </div>
      </div>
      
      <!-- 退出登录 -->
      <div v-if="isLoggedIn" class="mt-8">
        <button @click="handleLogout" class="w-full btn-secondary">
          <i class="fas fa-sign-out-alt mr-2"></i>
          退出登录
        </button>
      </div>
    </div>

    <BottomNav />
  </div>
</template>

<script>
import StatusBar from '@/components/StatusBar.vue'
import BottomNav from '@/components/BottomNav.vue'
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'Profile',
  components: {
    StatusBar,
    BottomNav
  },
  data() {
    return {
      shortcuts: [
        {
          id: 1,
          name: '充值',
          icon: 'fas fa-wallet',
          bgClass: 'bg-gradient-to-br from-green-500 to-blue-500',
          action: 'recharge'
        },
        {
          id: 2,
          name: 'VIP',
          icon: 'fas fa-crown',
          bgClass: 'bg-gradient-to-br from-yellow-500 to-orange-500',
          action: 'vip'
        },
        {
          id: 3,
          name: '收藏',
          icon: 'fas fa-heart',
          bgClass: 'bg-gradient-to-br from-red-500 to-pink-500',
          action: 'favorites'
        },
        {
          id: 4,
          name: '历史',
          icon: 'fas fa-history',
          bgClass: 'bg-gradient-to-br from-purple-500 to-indigo-500',
          action: 'history'
        }
      ],
      menuItems: [
        {
          id: 1,
          name: '个人资料',
          icon: 'fas fa-user-edit',
          action: 'profile'
        },
        {
          id: 2,
          name: '我的订单',
          icon: 'fas fa-shopping-bag',
          action: 'orders'
        },
        {
          id: 3,
          name: '下载管理',
          icon: 'fas fa-download',
          action: 'downloads'
        },
        {
          id: 4,
          name: '消息通知',
          icon: 'fas fa-bell',
          action: 'notifications'
        },
        {
          id: 5,
          name: '设置',
          icon: 'fas fa-cog',
          action: 'settings'
        },
        {
          id: 6,
          name: '帮助与反馈',
          icon: 'fas fa-question-circle',
          action: 'help'
        },
        {
          id: 7,
          name: '关于我们',
          icon: 'fas fa-info-circle',
          action: 'about'
        }
      ]
    }
  },
  computed: {
    ...mapGetters('user', ['isLoggedIn', 'userInfo', 'vipLevelText', 'balance']),
    
    displayName() {
      if (this.isLoggedIn && this.userInfo) {
        return this.userInfo.username || this.userInfo.nickname || '用户'
      }
      return '未登录'
    }
  },
  methods: {
    ...mapActions('user', ['logout']),
    
    goToLogin() {
      this.$router.push('/login')
    },
    
    handleShortcut(action) {
      if (!this.isLoggedIn && action !== 'vip') {
        this.goToLogin()
        return
      }
      
      switch (action) {
        case 'recharge':
          this.$router.push('/recharge')
          break
        case 'vip':
          this.$router.push('/vip')
          break
        case 'favorites':
          alert('收藏功能开发中...')
          break
        case 'history':
          alert('历史记录功能开发中...')
          break
      }
    },
    
    handleMenu(action) {
      if (!this.isLoggedIn) {
        this.goToLogin()
        return
      }
      
      switch (action) {
        case 'profile':
          alert('个人资料编辑功能开发中...')
          break
        case 'orders':
          alert('订单管理功能开发中...')
          break
        case 'downloads':
          alert('下载管理功能开发中...')
          break
        case 'notifications':
          alert('消息通知功能开发中...')
          break
        case 'settings':
          alert('设置功能开发中...')
          break
        case 'help':
          alert('帮助与反馈功能开发中...')
          break
        case 'about':
          alert('关于我们：\n\n时尚先锋摄影APP\n版本：1.0.0\n\n专业的时尚摄影内容平台\n发现时尚之美，记录精彩瞬间')
          break
      }
    },
    
    async handleLogout() {
      const confirmed = confirm('确认退出登录吗？')
      if (confirmed) {
        await this.logout()
        alert('已退出登录')
      }
    }
  }
}
</script>
